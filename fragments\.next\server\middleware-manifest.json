{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/ssr/_3222cf._.js", "server/edge/chunks/ssr/middleware_ts_8a0420._.js", "server/edge/chunks/ssr/edge-wrapper_bc7dd4.js", "server/edge/chunks/ssr/edge-wrapper_4033f5.js"], "name": "middleware", "page": "/", "matchers": [{"locale": false, "originalSource": "/s/:path*", "regexp": "^/s(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?$"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "P0o3lu7EEDsprs61+5FzXO14F0DF6c1/SeZcNChlW0Q=", "__NEXT_PREVIEW_MODE_ID": "ffd1cf0706468b6b0b4ff5538dcbd746", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e21b7b6fe685d373853e37b5cdf62390896c180892fb62ae3da39a8d0b481381", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3a323afeebfc2af092e77ee494d89a4ba3ca7e5e9ff843267c17b5ca9604773a"}}}, "sortedMiddleware": ["/"], "functions": {}}