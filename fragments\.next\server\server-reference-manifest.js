self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"b9abb214c1d81eb4fcb1a57e2ca2d748da3cbd75\": {\n      \"workers\": {\n        \"app/page\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/publish.ts [app-rsc] (ecmascript, action, ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/actions/validate-email.ts [app-rsc] (ecmascript, action, ecmascript)\\\" } [app-rsc] (ecmascript)\"\n      },\n      \"layer\": {\n        \"app/page\": \"actionBrowser\"\n      }\n    },\n    \"5664058e140f674604ef22271bddc5a7a8b87422\": {\n      \"workers\": {\n        \"app/page\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/actions/publish.ts [app-rsc] (ecmascript, action, ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/app/actions/validate-email.ts [app-rsc] (ecmascript, action, ecmascript)\\\" } [app-rsc] (ecmascript)\"\n      },\n      \"layer\": {\n        \"app/page\": \"actionBrowser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"P0o3lu7EEDsprs61+5FzXO14F0DF6c1/SeZcNChlW0Q=\"\n}"