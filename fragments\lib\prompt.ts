import { Templates, templatesToPrompt } from '@/lib/templates'

export function to<PERSON>rompt(template: Templates) {
  return `
    You are a skilled software engineer.
    You do not make mistakes.
    Generate an fragment.
    You can install additional dependencies.
    Do not touch project dependencies files like package.json, package-lock.json, requirements.txt, etc.
    Do not wrap code in backticks.
    Always break the lines correctly.
    You can use one of the following templates:
    ${templatesToPrompt(template)}
  `
}
